#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乙醇-水xy相图分析脚本
包含数据平滑、拟合和详细偏差分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import interpolate
from scipy.optimize import curve_fit
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图形样式
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 9
matplotlib.rcParams['ytick.labelsize'] = 9
matplotlib.rcParams['legend.fontsize'] = 9
plt.style.use('default')  # 使用默认样式以确保字体兼容性

def setup_chinese_font():
    """设置中文字体"""
    import matplotlib.font_manager as fm

    # 尝试找到可用的中文字体
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong',
                    'Arial Unicode MS', 'Noto Sans CJK SC', 'Source Han Sans SC']

    available_fonts = [f.name for f in fm.fontManager.ttflist]

    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font] + plt.rcParams['font.sans-serif']
            print(f"使用字体: {font}")
            return font

    # 如果没有找到中文字体，使用默认字体并警告
    print("警告: 未找到中文字体，可能会出现中文显示问题")
    return None

# 设置字体
setup_chinese_font()

class EthanolWaterAnalysis:
    def __init__(self):
        """初始化分析类"""
        self.x_data = None
        self.y_data = None
        self.x_smooth = None
        self.y_smooth = None
        self.fitted_params = None
        self.fitted_function = None
        self.fitting_method = None  # 记录使用的拟合方法
        
    def load_data(self):
        """加载乙醇-水平衡数据"""
        # 原始数据（从md文件中提取）
        data = {
            'x_liquid': [0.00, 2.01, 5.07, 7.95, 10.48, 14.95, 20.00, 25.00, 30.01, 35.09, 
                        40.00, 45.41, 50.16, 54.00, 59.55, 64.05, 70.63, 75.99, 79.82, 85.97, 89.41],
            'y_vapor': [0.00, 18.38, 33.06, 40.18, 44.61, 49.77, 53.09, 55.48, 57.70, 59.55,
                       61.44, 63.43, 65.34, 66.92, 69.59, 71.86, 75.82, 79.26, 81.83, 86.40, 89.41],
            'temperature': [100.00, 94.95, 90.50, 87.70, 86.20, 84.50, 83.30, 82.35, 81.60, 81.20,
                           80.75, 80.40, 80.00, 79.75, 79.55, 79.30, 78.85, 78.60, 78.40, 78.20, 78.15],
            'alpha': ['N/A', 10.978, 9.247, 7.777, 6.880, 5.637, 4.527, 3.739, 3.181, 2.723,
                     2.390, 2.085, 1.873, 1.723, 1.554, 1.433, 1.304, 1.207, 1.139, 1.037, 1.000]
        }
        
        df = pd.DataFrame(data)
        # 转换为小数形式
        self.x_data = np.array(df['x_liquid']) / 100.0
        self.y_data = np.array(df['y_vapor']) / 100.0
        
        print("数据加载完成:")
        print(f"数据点数量: {len(self.x_data)}")
        print(f"液相摩尔分数范围: {self.x_data.min():.3f} - {self.x_data.max():.3f}")
        print(f"气相摩尔分数范围: {self.y_data.min():.3f} - {self.y_data.max():.3f}")
        
        return df
    
    def create_smooth_curve(self, method='cubic', points=1000):
        """创建平滑曲线"""
        print(f"\n使用{method}方法创建平滑曲线...")
        
        if method == 'cubic':
            # 三次样条插值
            cs = interpolate.CubicSpline(self.x_data, self.y_data, bc_type='natural')
            self.x_smooth = np.linspace(self.x_data.min(), self.x_data.max(), points)
            self.y_smooth = cs(self.x_smooth)
            
        elif method == 'pchip':
            # 保形三次Hermite插值
            pchip = interpolate.PchipInterpolator(self.x_data, self.y_data)
            self.x_smooth = np.linspace(self.x_data.min(), self.x_data.max(), points)
            self.y_smooth = pchip(self.x_smooth)
            
        elif method == 'akima':
            # Akima插值
            akima = interpolate.Akima1DInterpolator(self.x_data, self.y_data)
            self.x_smooth = np.linspace(self.x_data.min(), self.x_data.max(), points)
            self.y_smooth = akima(self.x_smooth)
        
        print(f"平滑曲线创建完成，包含{len(self.x_smooth)}个点")
        return self.x_smooth, self.y_smooth
    
    def antoine_equation(self, x, A, B, C, D, E):
        """Antoine方程的修改形式用于xy相图拟合"""
        return A + B*x + C*x**2 + D*x**3 + E*x**4
    
    def wilson_equation(self, x, A12, A21):
        """Wilson方程用于活度系数计算"""
        gamma1 = np.exp(-np.log(x + (1-x)*A12) + (1-x)*(A12/(x + (1-x)*A12) - A21/((1-x) + x*A21)))
        return gamma1
    
    def fit_polynomial(self, degree=4):
        """多项式拟合（强制通过原点）"""
        print(f"\n进行{degree}次多项式拟合（强制通过原点）...")

        # 创建设计矩阵，不包含常数项（强制通过原点）
        X = np.vander(self.x_smooth, degree, increasing=True)[:, 1:]  # 去掉常数项列

        # 最小二乘拟合
        coeffs_no_const = np.linalg.lstsq(X, self.y_smooth, rcond=None)[0]

        # 添加零常数项
        coeffs = np.concatenate([[0], coeffs_no_const])
        poly_func = np.poly1d(coeffs[::-1])  # numpy的poly1d需要反向系数

        self.fitted_function = poly_func
        self.fitted_params = coeffs[::-1]  # 保持从高次到低次的顺序

        # 计算拟合优度
        y_fitted = poly_func(self.x_smooth)
        r2 = 1 - np.sum((self.y_smooth - y_fitted)**2) / np.sum((self.y_smooth - np.mean(self.y_smooth))**2)

        print(f"拟合完成，R² = {r2:.6f}")
        print("拟合系数（从高次到低次）:", self.fitted_params)

        return poly_func, self.fitted_params, r2

    def fit_rational_function(self):
        """有理函数拟合 y = (ax + bx²) / (1 + cx + dx²)"""
        print("\n进行有理函数拟合...")

        def rational_func(x, a, b, c, d):
            return (a*x + b*x**2) / (1 + c*x + d*x**2)

        try:
            # 初始猜测
            p0 = [1.0, 1.0, 0.1, 0.1]

            # 拟合
            popt, pcov = curve_fit(rational_func, self.x_smooth, self.y_smooth, p0=p0, maxfev=5000)

            # 创建拟合函数
            def fitted_rational(x):
                return rational_func(x, *popt)

            # 计算拟合优度
            y_fitted = fitted_rational(self.x_smooth)
            r2 = 1 - np.sum((self.y_smooth - y_fitted)**2) / np.sum((self.y_smooth - np.mean(self.y_smooth))**2)

            print(f"有理函数拟合完成，R² = {r2:.6f}")
            print(f"参数: a={popt[0]:.6f}, b={popt[1]:.6f}, c={popt[2]:.6f}, d={popt[3]:.6f}")

            return fitted_rational, popt, r2

        except Exception as e:
            print(f"有理函数拟合失败: {e}")
            return None, None, 0

    def calculate_deviations(self):
        """计算各种偏差指标"""
        print("\n计算偏差指标...")

        y_fitted = self.fitted_function(self.x_smooth)

        # 绝对偏差
        abs_deviation = np.abs(self.y_smooth - y_fitted)

        # 相对偏差（避免除零）
        rel_deviation = np.where(self.y_smooth > 1e-6, abs_deviation / self.y_smooth * 100, 0)

        # 统计指标
        mae = np.mean(abs_deviation)  # 平均绝对误差
        rmse = np.sqrt(np.mean((self.y_smooth - y_fitted)**2))  # 均方根误差
        max_abs_dev = np.max(abs_deviation)  # 最大绝对偏差
        max_rel_dev = np.max(rel_deviation)  # 最大相对偏差

        # 找出最大偏差点的位置
        max_dev_idx = np.argmax(abs_deviation)
        max_dev_x = self.x_smooth[max_dev_idx]
        max_dev_y_smooth = self.y_smooth[max_dev_idx]
        max_dev_y_fitted = y_fitted[max_dev_idx]

        deviation_stats = {
            'mae': mae,
            'rmse': rmse,
            'max_abs_dev': max_abs_dev,
            'max_rel_dev': max_rel_dev,
            'max_dev_x': max_dev_x,
            'max_dev_y_smooth': max_dev_y_smooth,
            'max_dev_y_fitted': max_dev_y_fitted,
            'abs_deviation': abs_deviation,
            'rel_deviation': rel_deviation
        }

        print(f"平均绝对误差 (MAE): {mae:.6f}")
        print(f"均方根误差 (RMSE): {rmse:.6f}")
        print(f"最大绝对偏差: {max_abs_dev:.6f}")
        print(f"最大相对偏差: {max_rel_dev:.2f}%")
        print(f"最大偏差位置: x={max_dev_x:.3f}, y_smooth={max_dev_y_smooth:.3f}, y_fitted={max_dev_y_fitted:.3f}")

        return deviation_stats

    def plot_comprehensive_analysis(self, deviation_stats):
        """绘制综合分析图"""
        print("\n绘制综合分析图...")

        fig = plt.figure(figsize=(20, 15))

        # 1. 主xy相图
        ax1 = plt.subplot(2, 3, 1)
        plt.scatter(self.x_data, self.y_data, c='red', s=80, alpha=0.8, label='原始数据点', zorder=5, edgecolors='darkred')
        plt.plot(self.x_smooth, self.y_smooth, 'b-', linewidth=2.5, label='平滑曲线', alpha=0.9)
        plt.plot(self.x_smooth, self.fitted_function(self.x_smooth), 'g--', linewidth=2.5, label='拟合曲线', alpha=0.9)
        plt.plot([0, 1], [0, 1], 'k:', alpha=0.6, label='y=x线', linewidth=1.5)

        plt.xlabel('液相摩尔分数 (x)', fontsize=11, fontweight='bold')
        plt.ylabel('气相摩尔分数 (y)', fontsize=11, fontweight='bold')
        plt.title('乙醇-水xy相图对比', fontsize=13, fontweight='bold', pad=15)
        plt.legend(frameon=True, fancybox=True, shadow=True, fontsize=9)
        plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        plt.axis('equal')
        plt.xlim(0, 1)
        plt.ylim(0, 1)

        # 添加一些关键点的标注
        for i in [0, 5, 10, 15, 20]:
            if i < len(self.x_data):
                plt.annotate(f'({self.x_data[i]:.2f}, {self.y_data[i]:.2f})',
                           (self.x_data[i], self.y_data[i]),
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=7, alpha=0.7)

        # 2. 绝对偏差分布
        ax2 = plt.subplot(2, 3, 2)
        plt.plot(self.x_smooth, deviation_stats['abs_deviation'], 'r-', linewidth=2.5, alpha=0.8)
        plt.axhline(y=deviation_stats['mae'], color='orange', linestyle='--', linewidth=2,
                   label=f'MAE = {deviation_stats["mae"]:.6f}')
        plt.axvline(x=deviation_stats['max_dev_x'], color='purple', linestyle=':', alpha=0.8, linewidth=2,
                   label=f'最大偏差位置')
        plt.xlabel('液相摩尔分数 (x)', fontsize=11, fontweight='bold')
        plt.ylabel('绝对偏差', fontsize=11, fontweight='bold')
        plt.title('绝对偏差分布', fontsize=13, fontweight='bold', pad=15)
        plt.legend(frameon=True, fancybox=True, shadow=True, fontsize=9)
        plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

        # 3. 相对偏差分布
        ax3 = plt.subplot(2, 3, 3)
        plt.plot(self.x_smooth, deviation_stats['rel_deviation'], 'purple', linewidth=2.5, alpha=0.8)
        plt.axhline(y=np.mean(deviation_stats['rel_deviation']), color='orange', linestyle='--', linewidth=2,
                   label=f'平均相对偏差 = {np.mean(deviation_stats["rel_deviation"]):.2f}%')
        plt.xlabel('液相摩尔分数 (x)', fontsize=11, fontweight='bold')
        plt.ylabel('相对偏差 (%)', fontsize=11, fontweight='bold')
        plt.title('相对偏差分布', fontsize=13, fontweight='bold', pad=15)
        plt.legend(frameon=True, fancybox=True, shadow=True, fontsize=9)
        plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

        # 4. 局部放大图（最大偏差区域）
        ax4 = plt.subplot(2, 3, 4)
        center_x = deviation_stats['max_dev_x']
        window = 0.1  # 窗口大小
        mask = (self.x_smooth >= center_x - window) & (self.x_smooth <= center_x + window)

        plt.scatter(self.x_data, self.y_data, c='red', s=60, alpha=0.6, label='原始数据点', edgecolors='darkred')
        plt.plot(self.x_smooth[mask], self.y_smooth[mask], 'b-', linewidth=3, label='平滑曲线', alpha=0.9)
        plt.plot(self.x_smooth[mask], self.fitted_function(self.x_smooth[mask]), 'g--', linewidth=3, label='拟合曲线', alpha=0.9)
        plt.scatter([deviation_stats['max_dev_x']], [deviation_stats['max_dev_y_smooth']],
                   c='blue', s=120, marker='o', label='平滑曲线点', edgecolors='darkblue', linewidth=2)
        plt.scatter([deviation_stats['max_dev_x']], [deviation_stats['max_dev_y_fitted']],
                   c='green', s=120, marker='s', label='拟合曲线点', edgecolors='darkgreen', linewidth=2)

        plt.xlabel('液相摩尔分数 (x)', fontsize=11, fontweight='bold')
        plt.ylabel('气相摩尔分数 (y)', fontsize=11, fontweight='bold')
        plt.title(f'最大偏差区域放大图 (x≈{center_x:.2f})', fontsize=13, fontweight='bold', pad=15)
        plt.legend(frameon=True, fancybox=True, shadow=True, fontsize=8)
        plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

        # 5. 残差分析
        ax5 = plt.subplot(2, 3, 5)
        residuals = self.y_smooth - self.fitted_function(self.x_smooth)
        plt.scatter(self.fitted_function(self.x_smooth), residuals, alpha=0.7, s=25, c='steelblue', edgecolors='navy', linewidth=0.5)
        plt.axhline(y=0, color='red', linestyle='--', linewidth=2, alpha=0.8)
        plt.xlabel('拟合值', fontsize=11, fontweight='bold')
        plt.ylabel('残差', fontsize=11, fontweight='bold')
        plt.title('残差分析图', fontsize=13, fontweight='bold', pad=15)
        plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

        # 添加残差统计信息
        residual_std = np.std(residuals)
        plt.axhline(y=2*residual_std, color='orange', linestyle=':', alpha=0.6, label=f'±2σ = ±{2*residual_std:.4f}')
        plt.axhline(y=-2*residual_std, color='orange', linestyle=':', alpha=0.6)
        plt.legend(fontsize=8)

        # 6. 统计信息文本
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')

        # 判断拟合方程类型 - 根据实际使用的拟合方法
        if self.fitting_method == 'rational':
            equation_type = "有理函数"
            equation_form = "y = (ax + bx²) / (1 + cx + dx²)"
            params_text = f"a = {self.fitted_params[0]:.6f}\nb = {self.fitted_params[1]:.6f}\nc = {self.fitted_params[2]:.6f}\nd = {self.fitted_params[3]:.6f}"
        else:
            equation_type = "多项式"
            equation_form = "y = a₄x⁴ + a₃x³ + a₂x² + a₁x + a₀"
            params_text = '\n'.join([f'a{len(self.fitted_params)-1-i} = {coef:.6f}' for i, coef in enumerate(self.fitted_params)])

        stats_text = f"""拟合质量评估报告

拟合方程类型: {equation_type}
{equation_form}

拟合参数:
{params_text}

偏差统计:
• 平均绝对误差 (MAE): {deviation_stats['mae']:.6f}
• 均方根误差 (RMSE): {deviation_stats['rmse']:.6f}
• 最大绝对偏差: {deviation_stats['max_abs_dev']:.6f}
• 最大相对偏差: {deviation_stats['max_rel_dev']:.2f}%

最大偏差位置:
• x = {deviation_stats['max_dev_x']:.3f}
• y_平滑 = {deviation_stats['max_dev_y_smooth']:.3f}
• y_拟合 = {deviation_stats['max_dev_y_fitted']:.3f}
• 绝对偏差 = {deviation_stats['max_abs_dev']:.6f}

拟合质量: {'优秀' if deviation_stats['max_rel_dev'] < 1.0 else '良好' if deviation_stats['max_rel_dev'] < 2.0 else '一般' if deviation_stats['max_rel_dev'] < 5.0 else '较差'}"""

        ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=9,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8, edgecolor='navy'))

        # 调整子图间距
        plt.tight_layout(pad=2.0, h_pad=2.5, w_pad=2.0)

        # 添加总标题
        fig.suptitle('乙醇-水xy相图详细分析报告', fontsize=16, fontweight='bold', y=0.98)

        # 保存高质量图像
        plt.savefig('ethanol_water_xy_analysis.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.show()

        return fig

    def generate_detailed_report(self, deviation_stats):
        """生成详细的分析报告"""
        print("\n" + "="*60)
        print("乙醇-水xy相图详细分析报告")
        print("="*60)

        print("\n1. 数据概况:")
        print(f"   - 原始数据点数: {len(self.x_data)}")
        print(f"   - 平滑曲线点数: {len(self.x_smooth)}")
        print(f"   - 浓度范围: {self.x_data.min():.1%} - {self.x_data.max():.1%}")

        print("\n2. 拟合方程:")
        if self.fitting_method == 'rational':
            print("   y = (ax + bx²) / (1 + cx + dx²)")
            print(f"   a = {self.fitted_params[0]:.8f}")
            print(f"   b = {self.fitted_params[1]:.8f}")
            print(f"   c = {self.fitted_params[2]:.8f}")
            print(f"   d = {self.fitted_params[3]:.8f}")
        else:
            print("   y = a₄x⁴ + a₃x³ + a₂x² + a₁x + a₀")
            for i, coef in enumerate(self.fitted_params):
                print(f"   a{len(self.fitted_params)-1-i} = {coef:.8f}")

        print("\n3. 偏差分析:")
        print(f"   - 平均绝对误差 (MAE): {deviation_stats['mae']:.6f}")
        print(f"   - 均方根误差 (RMSE): {deviation_stats['rmse']:.6f}")
        print(f"   - 最大绝对偏差: {deviation_stats['max_abs_dev']:.6f}")
        print(f"   - 最大相对偏差: {deviation_stats['max_rel_dev']:.2f}%")

        print("\n4. 关键偏差位置分析:")
        print(f"   - 最大偏差发生在 x = {deviation_stats['max_dev_x']:.3f}")
        print(f"   - 该点平滑曲线值: y = {deviation_stats['max_dev_y_smooth']:.6f}")
        print(f"   - 该点拟合曲线值: y = {deviation_stats['max_dev_y_fitted']:.6f}")
        print(f"   - 绝对偏差: {deviation_stats['max_abs_dev']:.6f}")
        print(f"   - 相对偏差: {deviation_stats['max_rel_dev']:.2f}%")

        # 分析偏差分布特征
        abs_dev = deviation_stats['abs_deviation']
        rel_dev = deviation_stats['rel_deviation']

        # 找出偏差较大的区域
        high_dev_threshold = np.percentile(abs_dev, 90)  # 90%分位数
        high_dev_mask = abs_dev > high_dev_threshold
        high_dev_regions = self.x_smooth[high_dev_mask]

        print("\n5. 偏差分布特征:")
        print(f"   - 偏差标准差: {np.std(abs_dev):.6f}")
        print(f"   - 90%分位数偏差: {high_dev_threshold:.6f}")
        if len(high_dev_regions) > 0:
            print(f"   - 高偏差区域: x ∈ [{high_dev_regions.min():.3f}, {high_dev_regions.max():.3f}]")

        print("\n6. 拟合质量评价:")
        if deviation_stats['max_rel_dev'] < 1.0:
            quality = "优秀"
        elif deviation_stats['max_rel_dev'] < 2.0:
            quality = "良好"
        elif deviation_stats['max_rel_dev'] < 5.0:
            quality = "一般"
        else:
            quality = "较差"

        print(f"   - 拟合质量等级: {quality}")
        print(f"   - 建议: ", end="")

        if deviation_stats['max_rel_dev'] < 1.0:
            print("拟合精度很高，可以用于工程计算")
        elif deviation_stats['max_rel_dev'] < 2.0:
            print("拟合精度较高，适用于大多数工程应用")
        else:
            print("建议考虑更高阶多项式或其他拟合方法")

        print("\n" + "="*60)


def main():
    """主函数"""
    print("开始乙醇-水xy相图分析...")

    # 创建分析对象
    analyzer = EthanolWaterAnalysis()

    # 加载数据
    df = analyzer.load_data()

    # 创建平滑曲线
    x_smooth, y_smooth = analyzer.create_smooth_curve(method='cubic', points=1000)

    # 进行多项式拟合（强制通过原点）
    poly_func, coeffs, r2_poly = analyzer.fit_polynomial(degree=4)

    # 尝试有理函数拟合
    rational_func, rational_params, r2_rational = analyzer.fit_rational_function()

    # 选择更好的拟合方法
    if rational_func is not None and r2_rational > r2_poly:
        print(f"\n选择有理函数拟合 (R² = {r2_rational:.6f})")
        analyzer.fitted_function = rational_func
        analyzer.fitted_params = rational_params
        analyzer.fitting_method = 'rational'
        best_r2 = r2_rational
    else:
        print(f"\n选择多项式拟合 (R² = {r2_poly:.6f})")
        analyzer.fitting_method = 'polynomial'
        best_r2 = r2_poly

    # 计算偏差
    deviation_stats = analyzer.calculate_deviations()

    # 绘制综合分析图
    fig = analyzer.plot_comprehensive_analysis(deviation_stats)

    # 生成详细报告
    analyzer.generate_detailed_report(deviation_stats)

    print(f"\n分析完成！图像已保存为 'ethanol_water_xy_analysis.png'")
    print(f"最终拟合优度 R² = {best_r2:.6f}")


if __name__ == "__main__":
    main()
